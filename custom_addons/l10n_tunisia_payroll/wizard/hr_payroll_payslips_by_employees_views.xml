<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_hr_payslip_by_employees" model="ir.ui.view">
        <field name="name">payroll_payslip_employees</field>
        <field name="model">hr.payslip.employees</field>
        <field name="arch" type="xml">
            <form string="Payslips by Employees">
                <header>
                    <button
                        icon="fa-cogs"
                        string="Generate"
                        name="compute_sheet"
                        type="object"
                        class="oe_highlight"
                    />
                </header>
                <group>
                    <span
                        colspan="4"
                        nolabel="1"
                    >This wizard will generate payslips for all selected employee(s) based on the dates and credit note specified on Payslips Run.</span>
                </group>
                <group colspan="4">
                    <separator string="Employees" colspan="4" />
                    <newline />
                    <field name="employee_ids" nolabel="1" />
                </group>
            </form>
        </field>
    </record>
    <record id="action_hr_payslip_by_employees" model="ir.actions.act_window">
        <field name="name">Generate Payslips</field>
        <field name="res_model">hr.payslip.employees</field>
        <field name="view_mode">tree,form</field>
        <field name="view_id" ref="view_hr_payslip_by_employees" />
        <field name="target">new</field>
    </record>
</odoo>
