<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="hr_payslip_view_tree" model="ir.ui.view">
        <field name="name">hr.payslip.tree</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <tree
                decoration-info="state in ('confirm','hr_check','accont_check')"
                decoration-muted="state == 'cancel'"
                string="Payslips"
            >
                <field name="number" />
                <field name="employee_id" />
                <field name="name" />
                <field name="date_from" />
                <field name="date_to" />
                <field name="state" />
                <field
                    name="company_id"
                    groups="base.group_multi_company"
                    options="{'no_create': True}"
                />
                <field name="payslip_run_id" invisible="1" />
            </tree>
        </field>
    </record>
    <record id="hr_payslip_view_kanban" model="ir.ui.view">
        <field name="name">hr.payslip.kanban</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                            <div class="row">
                                <div class="col-6">
                                    <strong>
                                        <field name="employee_id" />
                                    </strong>
                                </div>
                                <div class="col-6">
                                    <span class="float-right badge badge-secondary">
                                        <field name="state" />
                                    </span>
                                </div>
                                <div class="col-12">
                                    <span>
                                        <field name="date_from" /> - <field
                                            name="date_to"
                                        />
                                    </span>
                                </div>
                                <div class="col-12">
                                    <span>
                                        <field name="name" />
                                    </span>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="hr_payslip_view_form" model="ir.ui.view">
        <field name="name">hr.payslip.form</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <form string="Payslip">
                <header>
                    <button
                        string="Confirm"
                        name="action_payslip_done"
                        type="object"
                        states="draft"
                        class="oe_highlight"
                    />
                    <button
                        string="Refund"
                        name="refund_sheet"
                        states="confirm,done"
                        type='object'
                    />
                    <button
                        string="Set to Draft"
                        name="action_payslip_draft"
                        type="object"
                        states="cancel"
                    />
                    <button
                        string="Compute Sheet"
                        name="compute_sheet"
                        type="object"
                        states="draft"
                        class="oe_highlight"
                    />
                    <button
                        string="Cancel Payslip"
                        name="action_payslip_cancel"
                        type="object"
                        states="draft,hr_check,confirm,verify"
                    />
                    <field
                        name="state"
                        widget="statusbar"
                        statusbar_visible="draft,confirm"
                    />
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button
                            name="%(hr_payslip_line_action_computation_details)d"
                            class="oe_stat_button"
                            icon="fa-money"
                            type="action"
                        >
                            <field
                                name="payslip_count"
                                widget="statinfo"
                                string="Payslip"
                                help="Payslip Computation Details"
                            />
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="employee_id" class="oe_edit_only" />
                        <h1>
                            <field name="employee_id" placeholder="Employee" />
                        </h1>
                    </div>
                    <group col="4">
                        <label for="date_from" string="Period" />
                        <div>
                        <field name="date_from" class="oe_inline" /> - <field
                                name="date_to"
                                class="oe_inline"
                            />
                    </div>
                        <field
                            name="contract_id"
                            domain="[('employee_id','=',employee_id),('date_start','&lt;=',date_to),'|',('date_end','&gt;=',date_from),('date_end','=',False)]"
                            context="{'default_employee_id': employee_id}"
                        />
                        <field name="number" />
                        <field
                            name="struct_id"
                            attrs="{'required':[('contract_id','!=',False)]}"
                        />
                        <field name="name" />
                        <field name="credit_note" />
                    </group>
                    <notebook>
                        <page string="Worked Days &amp; Inputs">
                            <separator string="Worked Days" />
                            <field name="worked_days_line_ids">
                                <tree string="Worked Days" editable="bottom">
                                    <field name="name" />
                                    <field name="code" />
                                    <field
                                        name="number_of_days"
                                        sum="Total Working Days"
                                    />
                                    <field name="number_of_hours" />
                                    <field name="contract_id" />
                                    <field name="sequence" invisible="True" />
                                </tree>
                                <form string="Worked Day">
                                    <group col="4">
                                        <field name="name" />
                                        <field name="code" />
                                        <field name="sequence" />
                                        <field name="number_of_days" />
                                        <field name="number_of_hours" />
                                        <field name="contract_id" />
                                    </group>
                                </form>
                            </field>
                            <separator string="Other Inputs" />
                            <field name="input_line_ids" colspan="4" nolabel="1">
                                <tree string="Input Data" editable="bottom">
                                    <field name="name" />
                                    <field name="code" />
                                    <field name="amount" />
                                    <field name="contract_id" />
                                    <field name="sequence" invisible="True" />
                                </tree>
                                <form string="Payslip Line">
                                    <group col="4">
                                        <field name="name" />
                                        <field name="code" />
                                        <field name="sequence" />
                                        <field name="amount" />
                                        <field name="contract_id" />
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page string="Salary Computation">
                            <field name="line_ids" colspan="4" nolabel="1">
                                <tree
                                    string="Salary Structure"
                                    editable="bottom"
                                    decoration-info="total == 0"
                                >
                                    <field name="name" />
                                    <field name="code" />
                                    <field name="category_id" />
                                    <field name="sequence" invisible="1" />
                                    <field name="quantity" />
                                    <field name="rate" />
                                    <field name="salary_rule_id" />
                                    <field name="amount" />
                                    <field name="total" />
                                </tree>
                                <form string="Payslip Line">
                                    <group col="4">
                                        <field name="name" />
                                        <field name="code" />
                                        <field name="category_id" />
                                        <field name="sequence" />
                                        <field name="quantity" />
                                        <field name="rate" />
                                        <field name="amount" />
                                        <field name="total" />
                                        <field name="salary_rule_id" />
                                    </group>
                                </form>
                            </field>
                        </page>
                        <page string="Details By Salary Rule Category">
                            <field
                                name="details_by_salary_rule_category" context="{'search_default_category_id':1}" domain="[('appears_on_payslip', '=', True)]">
                                <tree
                                    string="Payslip Lines"
                                    decoration-info="total == 0"
                                >
                                    <field name="category_id" />
                                    <field name="name" />
                                    <field name="code" />
                                    <field name="total" />
                                </tree>
                            </field>
                        </page>
                        <page string="Accounting Information">
                            <group>
                                <group string="Miscellaneous">
                                    <field
                                        name="company_id"
                                        groups="base.group_multi_company"
                                    />
                                    <field
                                        name="payslip_run_id"
                                        domain="[('state','=','draft')]"
                                    />
                                </group>
                                <group name="accounting" string="Accounting">
                                    <field name="paid" readonly="1" />
                                </group>
                            </group>
                            <div colspan="4">
                                <field
                                    name="note"
                                    placeholder="Add an internal note..."
                                />
                            </div>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>
    <record id="hr_payslip_view_search" model="ir.ui.view">
        <field name="name">hr.payslip.select</field>
        <field name="model">hr.payslip</field>
        <field name="arch" type="xml">
            <search string="Search Payslips">
                <field
                    name="name"
                    string="Payslips"
                    filter_domain="['|',('name','ilike',self),('number','ilike',self)]"
                />
                <field name="date_from" />
                <filter
                    string="Draft"
                    name="draft"
                    domain="[('state','=','draft')]"
                    help="Draft Slip"
                />
                <filter
                    string="Done"
                    name="done"
                    domain="[('state','=','done')]"
                    help="Done Slip"
                />
                <field name="employee_id" />
                <field name="payslip_run_id" />
                <group expand="0" string="Group By">
                    <filter
                        string="Employees"
                        name="employee_id"
                        context="{'group_by':'employee_id'}"
                    />
                    <filter
                        string="PaySlip Batch"
                        name="payslip_run_id"
                        context="{'group_by':'payslip_run_id'}"
                    />
                    <filter
                        string="Companies"
                        name="company_id"
                        groups="base.group_multi_company"
                        context="{'group_by':'company_id'}"
                    />
                    <filter
                        string="States"
                        name="state"
                        context="{'group_by':'state'}"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="hr_payslip_action" model="ir.actions.act_window">
        <field name="name">Employee Payslips</field>
        <field name="res_model">hr.payslip</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="search_view_id" ref="hr_payslip_view_search" />
    </record>
    <record id="hr_payslip_action_employee" model="ir.actions.act_window">
        <field name="res_model">hr.payslip</field>
        <field name="name">Payslips</field>
        <field name="view_mode">tree,form</field>
        <field
            name="context"
        >{'search_default_employee_id': [active_id], 'default_employee_id': active_id}</field>
    </record>
    <menuitem
        id="hr_payslip_menu"
        action="hr_payslip_action"
        parent="payroll_menu_root"
        groups="l10n_tunisia_payroll.group_payroll_user"
    />
</odoo>
