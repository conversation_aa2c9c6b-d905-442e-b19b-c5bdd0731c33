<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="open_payroll_modules" model="ir.actions.act_window">
        <field name="name">Payroll</field>
        <field name="res_model">ir.module.module</field>
        <field name="view_mode">kanban,tree,form</field>
        <field
            name="context"
            eval="{'search_default_category_id': ref('base.module_category_accounting_localizations'), 'search_default_name': 'Payroll'}"
        />
        <field name="search_view_id" ref="base.view_module_filter" />
    </record>

    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.hr.payroll</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="45" />
        <field name="inherit_id" ref="base.res_config_settings_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//div[hasclass('settings')]" position="inside">
                <div
                    class="app_settings_block"
                    data-string="Payroll"
                    string="Payroll"
                    data-key="payroll"
                    groups="l10n_tunisia_payroll.group_payroll_manager"
                >
                    <h2>Accounting</h2>
                    <div class="row mt16 o_settings_container" id="payroll_accountant">
                        <div class="col-lg-6 col-12 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="module_payroll_account" />
                            </div>
                            <div class="o_setting_right_pane">
                                <label
                                    for="module_payroll_account"
                                    string="Payroll Entries"
                                />
                                <div class="text-muted">
                                    Post payroll slips in accounting
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
    <record id="payroll_configuration_action" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'l10n_tunisia_payroll'}</field>
    </record>
    <menuitem
        id="menu_payroll_global_settings"
        name="Settings"
        parent="payroll_menu_configuration"
        sequence="0"
        action="payroll_configuration_action"
        groups="base.group_system"
    />
</odoo>
