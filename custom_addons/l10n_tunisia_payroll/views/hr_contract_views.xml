<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="hr_contract_view_form" model="ir.ui.view">
        <field name="name">hr.contract.view.form.payroll</field>
        <field name="model">hr.contract</field>
        <field name="inherit_id" ref="hr_contract.hr_contract_view_form" />
        <field name="arch" type="xml">
            <xpath expr="//group[@name='salary']" position="inside">
                <field name="struct_id" required="1" />
                <field name="company_id" groups="base.group_multi_company" />
                <field name="currency_id" invisible="1" />
            </xpath>
            <xpath expr="//field[@name='resource_calendar_id']" position="after">
                <field name="schedule_pay" string="Fréquence de paiement"/>
            </xpath>
            <xpath expr="//field[@name='struct_id']" position="before">
                <field name="mois" string="Mois :"/>
            </xpath>
        </field>
    </record>
</odoo>
