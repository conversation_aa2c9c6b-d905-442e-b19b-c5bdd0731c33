<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="hr_salary_rule_view_tree_children" model="ir.ui.view">
        <field name="name">hr.salary.rule.list</field>
        <field name="model">hr.salary.rule</field>
        <field name="arch" type="xml">
            <tree string="Salary Rules">
                <field name="name" />
                <field name="code" />
                <field name="category_id" />
                <field name="sequence" invisible="1" />
                <field name="register_id" />
            </tree>
        </field>
    </record>
    <record id="hr_salary_rule_view_kanban" model="ir.ui.view">
        <field name="name">hr.salary.rule.kanban</field>
        <field name="model">hr.salary.rule</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                            <div class="row">
                                <div class="col-8">
                                    <strong>
                                        <field name="name" />
                                    </strong>
                                </div>
                                <div class="col-4">
                                    <span class="float-right">
                                        <field name="category_id" />
                                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span>Code: <field name="code" /></span>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="hr_salary_rule_view_tree" model="ir.ui.view">
        <field name="name">hr.salary.rule.tree</field>
        <field name="model">hr.salary.rule</field>
        <field name="field_parent">child_ids</field>
        <field eval="20" name="priority" />
        <field name="arch" type="xml">
            <tree string="Salary Rules">
                <field name="name" />
                <field name="code" />
                <field name="category_id" invisible="True" />
                <field
                    name="company_id"
                    groups="base.group_multi_company"
                    options="{'no_create': True}"
                />
            </tree>
        </field>
    </record>
    <record id="hr_salary_rule_view_form" model="ir.ui.view">
        <field name="name">hr.salary.rule.form</field>
        <field name="model">hr.salary.rule</field>
        <field name="arch" type="xml">
            <form string="Salary Rules">
                <label for="name" class="oe_edit_only" />
                <h1>
                    <field name="name" />
                </h1>
                <label for="category_id" class="oe_edit_only" />
                <h2>
                    <field name="category_id" />
                </h2>
                <group col="4">
                    <field name="code" />
                    <field name="sequence" />
                    <field name="active" />
                    <field name="appears_on_payslip" />
                    <field
                        name="company_id"
                        options="{'no_create': True}"
                        groups="base.group_multi_company"
                    />
                </group>
                <notebook colspan="6">
                    <page string="General">
                        <group col="4">
                            <separator colspan="4" string="Conditions" />
                            <field name="condition_select" />
                            <newline />
                            <field
                                name="condition_python"
                                attrs="{'invisible':[('condition_select','!=','python')], 'required': [('condition_select','=','python')]}"
                                colspan="4"
                            />
                            <newline />
                            <field
                                name="condition_range"
                                attrs="{'invisible':[('condition_select','!=','range')], 'required':[('condition_select','=','range')]}"
                            />
                            <newline />
                            <field
                                name="condition_range_min"
                                colspan="2"
                                attrs="{'invisible':[('condition_select','!=','range')], 'required':[('condition_select','=','range')]}"
                            />
                            <newline />
                            <field
                                name="condition_range_max"
                                colspan="2"
                                attrs="{'invisible':[('condition_select','!=','range')], 'required':[('condition_select','=','range')]}"
                            />
                            <newline />
                            <separator colspan="4" string="Computation" />
                            <field name="amount_select" />
                            <newline />
                            <field
                                name="amount_percentage_base"
                                attrs="{'invisible':[('amount_select','!=','percentage')], 'required': [('amount_select','=','percentage')]}"
                            />
                            <newline />
                            <field
                                name="quantity"
                                attrs="{'invisible':[('amount_select','=','code')], 'required':[('amount_select','!=','code')]}"
                            />
                            <newline />
                            <field
                                name="amount_fix"
                                attrs="{'invisible':[('amount_select','!=','fix')], 'required':[('amount_select','=','fix')]}"
                            />
                            <newline />
                            <field
                                colspan="4"
                                name="amount_python_compute"
                                attrs="{'invisible':[('amount_select','!=','code')], 'required':[('amount_select','=','code')]}"
                            />
                            <field
                                name="amount_percentage"
                                attrs="{'invisible':[('amount_select','!=','percentage')], 'required':[('amount_select','=','percentage')]}"
                            />
                            <separator colspan="4" string="Company Contribution" />
                            <field name="register_id" />
                        </group>
                    </page>
                    <page name="rules" string="Child Rules">
                        <field name="parent_rule_id" />
                        <separator string="Children Definition" />
                        <field name="child_ids" />
                    </page>
                    <page string="Inputs">
                        <field name="input_ids" mode="tree">
                            <tree string="Input Data" editable="bottom">
                                <field name="name" />
                                <field name="code" />
                            </tree>
                        </field>
                    </page>
                    <page string="Description">
                        <field name="note" />
                    </page>
                </notebook>
            </form>
        </field>
    </record>
    <record id="hr_salary_rule_view_search" model="ir.ui.view">
        <field name="name">hr.salary.rule.select</field>
        <field name="model">hr.salary.rule</field>
        <field name="arch" type="xml">
            <search string="Search Salary Rule">
                <field
                    name="name"
                    string="Salary Rules"
                    filter_domain="['|',('name','ilike',self),('code','ilike',self)]"
                />
                <field name="category_id" />
                <field name="condition_range_min" />
                <group col="8" colspan="4" expand="0" string="Group By">
                    <filter
                        string="Category"
                        name="head"
                        context="{'group_by':'category_id'}"
                    />
                </group>
            </search>
        </field>
    </record>
    <record id="action_salary_rule_form" model="ir.actions.act_window">
        <field name="name">Salary Rules</field>
        <field name="res_model">hr.salary.rule</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="domain">[('parent_rule_id','=',False)]</field>
        <field name="search_view_id" ref="hr_salary_rule_view_search" />
    </record>
    <menuitem
        id="menu_action_hr_salary_rule_form"
        action="action_salary_rule_form"
        parent="payroll_menu_configuration"
        sequence="12"
    />
    <act_window
        name="All Children Rules"
        domain="[('parent_rule_id', '=', active_id)]"
        res_model="hr.salary.rule"
        view_id="hr_salary_rule_view_tree_children"
        id="act_children_salary_rules"
    />
</odoo>
