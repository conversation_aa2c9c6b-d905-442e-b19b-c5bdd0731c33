<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="hr_contribution_register_view_tree" model="ir.ui.view">
        <field name="name">hr.contribution.register.tree</field>
        <field name="model">hr.contribution.register</field>
        <field name="arch" type="xml">
            <tree string="Contribution Registers">
                <field name="name" />
                <field
                    name="company_id"
                    groups="base.group_multi_company"
                    options="{'no_create': True}"
                />
            </tree>
        </field>
    </record>
    <record id="hr_contribution_register_view_kanban" model="ir.ui.view">
        <field name="name">hr.contribution.register.kanban</field>
        <field name="model">hr.contribution.register</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_content oe_kanban_global_click">
                            <div class="row">
                                <div class="col-12">
                                    <strong>
                                        <field name="name" />
                                    </strong>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="hr_contribution_register_view_search" model="ir.ui.view">
        <field name="name">hr.contribution.register.search</field>
        <field name="model">hr.contribution.register</field>
        <field name="arch" type="xml">
            <search string="Contribution Registers">
                <field name="name" string="Contribution Registers" />
                <field name="company_id" groups="base.group_multi_company" />
            </search>
        </field>
    </record>
    <record id="hr_contribution_register_view_form" model="ir.ui.view">
        <field name="name">hr.contribution.register.form</field>
        <field name="model">hr.contribution.register</field>
        <field name="arch" type="xml">
            <form string="Contribution">
                <group>
                    <field name="name" />
                    <field name="partner_id" />
                    <field
                        name="company_id"
                        groups="base.group_multi_company"
                        options="{'no_create': True}"
                        class="oe_inline"
                    />
                </group>
                <newline />
                <group>
                    <separator string="Description" />
                    <newline />
                    <field name="note" nolabel="1" />
                </group>
            </form>
        </field>
    </record>
    <record id="hr_contribution_register_action" model="ir.actions.act_window">
        <field name="name">Contribution Registers</field>
        <field name="res_model">hr.contribution.register</field>
        <field name="view_mode">tree,kanban,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
            Add a new contribution register
          </p>
            <p>
            A contribution register is a third party involved in the salary
            payment of the employees. It can be the social security, the
            state or anyone that collect or inject money on payslips.
          </p>
        </field>
    </record>
    <menuitem
        id="hr_contribution_register_menu"
        action="hr_contribution_register_action"
        parent="payroll_menu_configuration"
        sequence="14"
    />
</odoo>
