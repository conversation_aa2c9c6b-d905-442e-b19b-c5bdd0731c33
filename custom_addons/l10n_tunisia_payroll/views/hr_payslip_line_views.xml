<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="hr_payslip_line_view_tree" model="ir.ui.view">
        <field name="name">hr.payslip.line.tree</field>
        <field name="model">hr.payslip.line</field>
        <field name="arch" type="xml">
            <tree
                string="Salary Structure"
                editable="bottom"
                decoration-info="total == 0"
            >
                <field name="category_id" />
                <field name="employee_id" invisible="1" />
                <field name="sequence" />
                <field name="name" />
                <field name="code" />
                <field name="quantity" />
                <field name="rate" />
                <field name="amount" />
                <field name="total" />
                <field name="amount_select" invisible="1" />
                <field name="register_id" invisible="1" />
            </tree>
        </field>
    </record>
    <record id="hr_payslip_line_view_form" model="ir.ui.view">
        <field name="name">hr.payslip.line.form</field>
        <field name="model">hr.payslip.line</field>
        <field name="arch" type="xml">
            <form string="Payslip Line">
                <group>
                    <group>
                        <field name="name" />
                        <field name="code" />
                        <field name="slip_id" />
                        <field name="employee_id" />
                    </group>
                    <group string="Calculations">
                        <field name="category_id" />
                        <field name="amount_select" />
                        <field
                            name="amount_fix"
                            attrs="{'readonly':[('amount_select','!=','fix')]}"
                        />
                        <field
                            name="amount_percentage"
                            attrs="{'readonly':[('amount_select','!=','percentage')]}"
                        />
                        <field name="sequence" />
                    </group>
                    <field name="note" />
                </group>
            </form>
        </field>
    </record>
    <record id="hr_payslip_line_view_search" model="ir.ui.view">
        <field name="name">hr.payslip.line.select</field>
        <field name="model">hr.payslip.line</field>
        <field name="arch" type="xml">
            <search string="Search Payslip Lines">
                <field
                    name="name"
                    string="Payslip Lines"
                    filter_domain="['|',('name','ilike',self),('code','ilike',self)]"
                />
                <field name="amount_select" />
                <field name="slip_id" />
                <group col="8" colspan="4" expand="0" string="Group By">
                    <filter
                        string="Salary Rule Category"
                        name="category_id"
                        context="{'group_by':'category_id'}"
                    />
                    <filter
                        string="Contribution Register"
                        name="register_id"
                        context="{'group_by':'register_id'}"
                    />
                    <filter
                        string="Amount Type"
                        name="amount_select"
                        context="{'group_by':'amount_select'}"
                    />
                    <filter
                        string="Employees"
                        name="employee_id"
                        context="{'group_by':'employee_id'}"
                    />
                </group>
            </search>
        </field>
    </record>
    <act_window
        name="Payslip Lines"
        domain="[('register_id', '=', active_id)]"
        context="{'default_register_id': active_id, 'search_default_register_id': 1}"
        res_model="hr.payslip.line"
        id="hr_payslip_line_action"
    />
    <act_window
        name="Payslip Computation Details"
        context="{'default_slip_id': active_id,'search_default_slip_id': active_id}"
        res_model="hr.payslip.line"
        id="hr_payslip_line_action_computation_details"
    />
</odoo>
