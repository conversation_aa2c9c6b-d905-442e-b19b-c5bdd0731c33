# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON>, 2019
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2020
# <PERSON> <<EMAIL>>, 2020
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-08 23:24+0000\n"
"PO-Revision-Date: 2018-08-24 09:19+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: French (https://www.transifex.com/odoo/teams/41243/fr/)\n"
"Language: fr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payroll_structure.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (copie)"

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,print_report_name:l10n_tunisia_payroll.action_report_payslip
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,print_report_name:l10n_tunisia_payroll.payslip_details_report
msgid "'Payslip Details - %s' % (object.employee_id.name)"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip__state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"        \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"        \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"        \n"
"* When user cancel payslip the status is 'Rejected'."
msgstr ""

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_hr_payslip_by_employees
msgid ""
"<span colspan=\"4\" nolabel=\"1\">This wizard will generate payslips for all "
"selected employee(s) based on the dates and credit note specified on "
"Payslips Run.</span>"
msgstr ""
"<span colspan=\"4\" nolabel=\"1\">Cet assistant va générer les fiches de "
"paie de(s) l'employé(s) sélectionné(s) sur base des dates et des notes de "
"crédit figurant sur les bulletins de paie.</span>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Address</strong>"
msgstr "<strong>Adresse</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Authorized signature</strong>"
msgstr "<strong>Signature autorisée</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Bank Account</strong>"
msgstr "<strong>Compte bancaire</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "<strong>Date From:</strong>"
msgstr "<strong>Date du:</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Date From</strong>"
msgstr "<strong>Date du</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "<strong>Date To:</strong>"
msgstr "<strong>Date au:</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Date To</strong>"
msgstr "<strong>Date au</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Designation</strong>"
msgstr "<strong>Désignation</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Email</strong>"
msgstr "<strong>Courriel</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Identification No</strong>"
msgstr "<strong>N° d'identification</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Name</strong>"
msgstr "<strong>Nom</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Reference</strong>"
msgstr "<strong>Référence</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "<strong>Register Name:</strong>"
msgstr "<strong>Nom de dossier:</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "<strong>Total</strong>"
msgstr "<strong>Total</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.actions.act_window,help:l10n_tunisia_payroll.hr_contribution_register_action
msgid ""
"A contribution register is a third party involved in the salary\n"
"            payment of the employees. It can be the social security, the\n"
"            state or anyone that collect or inject money on payslips."
msgstr ""

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.res_config_settings_view_form
msgid "Accounting"
msgstr "Comptabilité"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Accounting Information"
msgstr "Informations comptables"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__active
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__active
msgid "Active"
msgstr "Actif"

#. module: l10n_tunisia_payroll
#: model_terms:ir.actions.act_window,help:l10n_tunisia_payroll.hr_contribution_register_action
msgid "Add a new contribution register"
msgstr ""

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Add an internal note..."
msgstr "Ajouter une note interne…"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contract_advantage_template_view_form
msgid "Advantage Name"
msgstr "Nom de l'avantage"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.act_children_salary_rules
msgid "All Children Rules"
msgstr "Toutes les règles enfant"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.ALW
msgid "Allowance"
msgstr "Allocation"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__condition_select__none
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__condition_select__none
msgid "Always True"
msgstr "Toujours vrai"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__amount
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Amount"
msgstr "Montant"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_select
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
msgid "Amount Type"
msgstr "Type de montant"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__annually
msgid "Annually"
msgstr "Annuel"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Appears on Payslip"
msgstr "Apparaît sur le bulletin de paie"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__condition_python
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""
"Appliquer cette règle pour le calcul si la condition est vraie. Vous pouvez "
"spécifier une condition comme base> 1000."

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.BASIC
msgid "Basic"
msgstr "Basique"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_rule_basic
msgid "Basic Salary"
msgstr "Salaire de Base"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__bi-monthly
msgid "Bi-monthly"
msgstr "Bi-mensuel"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__bi-weekly
msgid "Bi-weekly"
msgstr "Bi-hebdomadaire"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_form
msgid "Calculations"
msgstr "Calculs"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_payslip_lines_contribution_register
msgid "Cancel"
msgstr "Annuler"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Cancel Payslip"
msgstr "Annuler la fiche de paie"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Cannot cancel a payslip that is done."
msgstr "Impossible d'annuler un bulletin de paie terminé."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__category_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__category_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_search
msgid "Category"
msgstr "Catégorie"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Child Rules"
msgstr "Règles enfant"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__child_ids
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__child_ids
msgid "Child Salary Rule"
msgstr "Règle de salaire enfant"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__children_ids
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__children_ids
msgid "Children"
msgstr "Enfants"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Children Definition"
msgstr "Définition des enfants"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_run__state__close
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
msgid "Close"
msgstr "Fermer"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__code
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Code"
msgstr "Code"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_kanban
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr "Code :"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Companies"
msgstr "Sociétés"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__company_id
msgid "Company"
msgstr "Société"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.COMP
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Company Contribution"
msgstr "Contribution de la société"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Computation"
msgstr "Calcul"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Compute Sheet"
msgstr "Calculer la feuille"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_select
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_select
msgid "Condition Based on"
msgstr "Condition basée sur"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Conditions"
msgstr "Conditions"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "Paramètres de config"

#. module: l10n_tunisia_payroll
#: model:ir.ui.menu,name:l10n_tunisia_payroll.payroll_menu_configuration
msgid "Configuration"
msgstr "Configuration"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Confirm"
msgstr "Confirmer"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__contract_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__contract_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__contract_id
msgid "Contract"
msgstr "Contrat"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_contract_advantage_template_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_contract_advantage_template_menu_action
msgid "Contract Advantage Templates"
msgstr "Modèles d'avantages de contrat"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contribution_register_view_form
msgid "Contribution"
msgstr "Contribution"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_contribution_register
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__register_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__register_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
msgid "Contribution Register"
msgstr "Registre de contribution"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_payslip_lines_contribution_register
msgid "Contribution Register's Payslip Lines"
msgstr "Registre des contribution des lignes de bulletin"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_contribution_register_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_contribution_register_menu
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contribution_register_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contribution_register_view_tree
msgid "Contribution Registers"
msgstr "Registres des contributions"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr "Indemnité de transport"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_ca_demo
msgid "Conveyance Allowance For Marc Demo"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__create_uid
msgid "Created by"
msgstr "Créé par"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__create_date
msgid "Created on"
msgstr "Créé le"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__credit_note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__credit_note
msgid "Credit Note"
msgstr "Avoir"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__date_from
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__date_start
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__date_from
msgid "Date From"
msgstr "Date début"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__date_to
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__date_end
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__date_to
msgid "Date To"
msgstr "Date de fin"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.DED
msgid "Deduction"
msgstr "Déduction"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__default_value
msgid "Default value for this advantage"
msgstr "Valeur par défaut pour cet avantage"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_contract__schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr "Définit la fréquence de paiement du salaire."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip__struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, accordingly to "
"the contract chosen. If you let empty the field contract, this field isn't "
"mandatory anymore and thus the rules applied will be all the rules set on "
"the structure of all contracts of the employee valid for the chosen period"
msgstr ""
"Défini les règles qui doivent être appliquées à ce bulletin de paie, "
"conformément au contrat choisi. Si vous laissez vide le champ contrat, ce "
"champ n'est plus obligatoire et donc les règles appliquées seront toutes les "
"règles établies sur la structure de tous les contrats valides de l'employé "
"pour la période déterminée"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__note
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contribution_register_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Description"
msgstr "Description"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Details By Salary Rule Category"
msgstr "Détails par catégorie de règle salariale"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__details_by_salary_rule_category
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Details by Salary Rule Category"
msgstr "Détails par catégorie de règle de salaire"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_contributionregister__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_payslipdetails__display_name
msgid "Display Name"
msgstr "Nom affiché"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip__state__done
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Done"
msgstr "Fait"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
msgid "Done Payslip Batches"
msgstr "Lots terminés de bulletins de paie"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Done Slip"
msgstr "Bulletin de paie terminé"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip__state__draft
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_run__state__draft
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Draft"
msgstr "Brouillon"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
msgid "Draft Payslip Batches"
msgstr "Lots brouillons de bulletins de paie"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Draft Slip"
msgstr "Bulletin de paie brouillon"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_employee
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__employee_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__employee_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Employee"
msgstr "Employé"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "Contrat de l'employé"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_tree
msgid "Employee Function"
msgstr "Fonction de l'employé"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_payslip_menu
msgid "Employee Payslips"
msgstr "Bulletins de l'employé"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_contract_advantage_template
msgid "Employee's Advantage on Contract"
msgstr "Avantage sur le contrat de l'employé"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_contract__resource_calendar_id
msgid "Employee's working schedule."
msgstr "Emploi du temps de l'employé."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__employee_ids
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_hr_payslip_by_employees
msgid "Employees"
msgstr "Employés"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule_category.py:0
#, python-format
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr ""
"Erreur ! Vous ne pouvez pas créer de hiérarchie récursive de Categorie de "
"Règles de Salaire."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Error! You cannot create recursive hierarchy of Salary Rules."
msgstr ""
"Erreur ! Vous ne pouvez pas créer de hiérarchie récursive de Règles de "
"Salaire."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__register_id
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__register_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr ""
"Éventuellement partie tierce impliquée dans le paiement des salaires des "
"employés."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_fix
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_fix
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__amount_select__fix
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__amount_select__fix
msgid "Fixed Amount"
msgstr "Montant fixe"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr "Par exemple, saisir 50.0 pour appliquer un pourcentage de 50%"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/report/report_contribution_register.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr ""
"Le contenu du formulaire est manquant, le rapport ne peut pas être imprimé. "

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "General"
msgstr "Général"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr "Générer"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.action_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
msgid "Generate Payslips"
msgstr "Générer les bulletins de paie"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "Génère les bulletins de paie pour tous les employés sélectionnés"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_sales_commission
msgid "Get 1% of sales"
msgstr "Récupérer 1% des ventes"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Global Leaves"
msgstr "Congés globaux"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_rule_taxable
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.GROSS
msgid "Gross"
msgstr "Brut"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_search
msgid "Group By"
msgstr "Regrouper par"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr "Allocation logement"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_contributionregister__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_payslipdetails__id
msgid "ID"
msgstr "ID"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_run__credit_note
msgid ""
"If its checked, indicates that all payslips generated from here are refund "
"payslips."
msgstr ""
"Si coché, indique que tous les bulletins de paie générés à partir d'ici sont "
"des bulletins de paie de remboursement."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__active
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""
"Si le champ actif est défini sur faux, la règle salariale sera masquée sans "
"être supprimée."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip__credit_note
msgid "Indicates this payslip has a refund of another"
msgstr "Indique que ce bulletin de paie est le remboursement d'un autre"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Input Data"
msgstr "Données d'entrée"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__input_ids
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__input_ids
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Inputs"
msgstr "Entrées"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__note
msgid "Internal Note"
msgstr "Note interne"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_kanban
msgid "Is a Blocking Reason?"
msgstr "Est-ce un motif de blocage ?"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__quantity
msgid ""
"It is used in computation for percentage and fixed amount. For e.g. A rule "
"for Meal Voucher having fixed amount of 1€ per worked day can have its "
"quantity defined in expression like worked_days.WORK100.number_of_days."
msgstr ""
"Utilisé dans le calcul du montant en pourcentage et en valeur. Par exemple, "
"dans le cas d'une règle pour une indemnité de repas ayant un montant fixe de "
"1 € par jour travaillé, la quantité peut être définie à l'aide d'une "
"expression de type worked_days.WORK100.number_of_days."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_input__amount
msgid ""
"It is used in computation. For e.g. A rule for sales having 1% commission of "
"basic salary for per product can defined in expression like result = inputs."
"SALEURO.amount * contract.wage*0.01."
msgstr ""
"Il est utilisé dans le calcul. Par exemple : une règle sur les ventes pour "
"quelqu'un percevant une commission de 1% du salaire de base par article peut "
"être défini par l'expression \"résultat = inputs.SALEURO.amount * contract."
"wage * 0,01\"."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_contributionregister____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_payslipdetails____last_update
msgid "Last Modified on"
msgstr "Dernière Modification le"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__write_uid
msgid "Last Updated by"
msgstr "Dernière mise à jour par"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__write_date
msgid "Last Updated on"
msgstr "Dernière mise à jour le"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule_category__parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr ""
"La liaison d'une catégorie salariale à son parent est utilisée uniquement "
"dans le but de rapports."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__lower_bound
msgid "Lower Bound"
msgstr "Limite inférieure"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_contract_advantage_template__lower_bound
msgid "Lower bound authorized by the employer for this advantage"
msgstr "La limite inférieure autorisée par l'employeur pour cet avantage"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__paid
msgid "Made Payment Order ? "
msgstr "Établir l'ordre de paiement "

#. module: l10n_tunisia_payroll
#: model:ir.module.category,description:l10n_tunisia_payroll.module_category_payroll
msgid "Manage employee payroll"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:res.groups,name:l10n_tunisia_payroll.group_payroll_manager
msgid "Manager"
msgstr "Gestionnaire"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_range_max
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_range_max
msgid "Maximum Range"
msgstr "Plage maximale"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr "Chèque Repas"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_range_min
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_range_min
msgid "Minimum Range"
msgstr "Plage minimum"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Miscellaneous"
msgstr "Divers"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__monthly
msgid "Monthly"
msgstr "Mensuel"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__name
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Name"
msgstr "Nom"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.NET
msgid "Net"
msgstr "Net"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_rule_net
msgid "Net Salary"
msgstr "Salaire Net"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Normal Working Days paid at 100%"
msgstr "Jours travaillés à 100%"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_category_view_form
msgid "Notes"
msgstr "Notes"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__number_of_days
msgid "Number of Days"
msgstr "Nombre de jours"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__number_of_hours
msgid "Number of Hours"
msgstr "Nombre d'Heures"

#. module: l10n_tunisia_payroll
#: model:res.groups,name:l10n_tunisia_payroll.group_payroll_user
msgid "Officer"
msgstr "Fonctionnaire"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Other Inputs"
msgstr "Autres entrées"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__parent_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__parent_id
msgid "Parent"
msgstr "Parent"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__parent_rule_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__parent_rule_id
msgid "Parent Salary Rule"
msgstr "Règle salariale parente"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__partner_id
msgid "Partner"
msgstr "Partenaire"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__payslip_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__slip_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__payslip_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Pay Slip"
msgstr "Feuille de paie"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "PaySlip Batch"
msgstr "Lot de bulletins de paie"

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,name:l10n_tunisia_payroll.payslip_details_report
msgid "PaySlip Details"
msgstr "Détails du bulletin de paie"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.action_payslip_lines_contribution_register
msgid "PaySlip Lines"
msgstr "Lignes du bulletin"

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,name:l10n_tunisia_payroll.action_contribution_register
msgid "PaySlip Lines By Conribution Register"
msgstr "Lignes du bulletin de salaire par registre de contribution"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "PaySlip Lines by Contribution Register"
msgstr "Lignes du bulletin par registre de contribution"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "PaySlip Name"
msgstr "Nom du bulletin"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.open_payroll_modules
#: model:ir.module.category,name:l10n_tunisia_payroll.module_category_payroll
#: model:ir.ui.menu,name:l10n_tunisia_payroll.payroll_menu_root
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr "Paie"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_res_config_settings__module_payroll_account
msgid "Payroll Accounting"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_report_payroll_report_contributionregister
msgid "Payroll Contribution Register Report"
msgstr ""

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr "Entrées de règles de paie"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_search
msgid "Payroll Structures"
msgstr "Structures des bulletins"

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,name:l10n_tunisia_payroll.action_report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Payslip"
msgstr "Feuille de paye"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Payslip 'Date From' must be earlier than 'Date To'."
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_run
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__payslip_run_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
msgid "Payslip Batches"
msgstr "Lots de bulletins de paie"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_line_action_computation_details
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__payslip_count
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Payslip Computation Details"
msgstr "Détails pour le calcul du bulletin"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_employee__payslip_count
msgid "Payslip Count"
msgstr "Décompte de la fiche de paie"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_report_payroll_report_payslipdetails
msgid "Payslip Details Report"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_input
msgid "Payslip Input"
msgstr "Entrée du bulletin"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__input_line_ids
msgid "Payslip Inputs"
msgstr "Entrées du bulletin de salaire"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Payslip Line"
msgstr "Ligne de bulletin de salaire"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_line_action
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__line_ids
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Payslip Lines"
msgstr "Lignes du bulletin de paie"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Payslip Lines by Contribution Register"
msgstr "Lignes de bulletin de paie par registre de contribution"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_payslip_lines_contribution_register
msgid "Payslip Lines by Contribution Registers"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__name
msgid "Payslip Name"
msgstr "Nom de bulletin de paie"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__worked_days_line_ids
msgid "Payslip Worked Days"
msgstr "Bulletin de paie jours travaillés"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_action_employee
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_employee__slip_ids
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__slip_ids
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_tree
msgid "Payslips"
msgstr "Feuilles de paye"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_run_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_payslip_run_menu
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_tree
msgid "Payslips Batches"
msgstr "Lots de bulletins de paie"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr "Bulletins de paie par employé"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_percentage
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__amount_select__percentage
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__amount_select__percentage
msgid "Percentage (%)"
msgstr "Pourcentage (%)"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_percentage_base
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_percentage_base
msgid "Percentage based on"
msgstr "Pourcentage basé sur"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Period"
msgstr "Période"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr "Transmettre les bulletins de paie au service comptabilité"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_payslip_lines_contribution_register
msgid "Print"
msgstr "Imprimer"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr "Taxe professionnelle"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr "Caisse de prévoyance"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_python_compute
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_python_compute
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__amount_select__code
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__amount_select__code
msgid "Python Code"
msgstr "Code Python"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_python
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_python
msgid "Python Condition"
msgstr "Condition Python"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__condition_select__python
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__condition_select__python
msgid "Python Expression"
msgstr "Expression Python"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__quantity
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__quantity
msgid "Quantity"
msgstr "Quantité"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "Quantity/Rate"
msgstr "Quantité/taux"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Quantity/rate"
msgstr "Quantité/taux"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__quarterly
msgid "Quarterly"
msgstr "Trimestriel"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__condition_select__range
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__condition_select__range
msgid "Range"
msgstr "Plage"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_range
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_range
msgid "Range Based on"
msgstr "Plage basée sur"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__rate
msgid "Rate (%)"
msgstr "Taux (%)"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__number
msgid "Reference"
msgstr "Référence"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Refund"
msgstr "Avoir"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Refund: %s"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__register_line_ids
msgid "Register Line"
msgstr "Ligne du registre"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip__state__cancel
msgid "Rejected"
msgstr "Rejeté"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__salary_rule_id
msgid "Rule"
msgstr "Règle"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_category_view_form
msgid "Salary Categories"
msgstr "Catégories de salaires"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Salary Computation"
msgstr "Calcul de salaire"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr "Règles salariales"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_salary_rule_category_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.menu_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_category_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_category_view_tree
msgid "Salary Rule Categories"
msgstr "Catégories de règles pour le salaire"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Salary Rule Category"
msgstr "Catégorie de règle salariale"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_rule_input
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__input_id
msgid "Salary Rule Input"
msgstr "Règle salariale en entrée"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__rule_ids
#: model:ir.ui.menu,name:l10n_tunisia_payroll.menu_action_hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_tree_children
msgid "Salary Rules"
msgstr "Règles salariales"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Salary Slip of %s for %s"
msgstr "Bulletin de paie de %s pour %s"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract__struct_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_tree_children
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Salary Structure"
msgstr "Structure salariale"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payroll_structure_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_payroll_structure_menu
msgid "Salary Structures"
msgstr "Structure des salaires"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract__schedule_pay
msgid "Scheduled Pay"
msgstr "Paie planifiée"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
msgid "Search Payslip Batches"
msgstr "Rechercher des lots de bulletin de paie"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
msgid "Search Payslip Lines"
msgstr "Rechercher des lignes de bulletins"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Search Payslips"
msgstr "Rechercher dans les feuilles de paye"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_search
msgid "Search Salary Rule"
msgstr "Rechercher une règle salariale"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__semi-annually
msgid "Semi-annually"
msgstr "Semestriel"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__sequence
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__sequence
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__sequence
msgid "Sequence"
msgstr "Séquence"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Set to Draft"
msgstr "Marquer comme brouillon"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.payroll_configuration_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.menu_payroll_global_settings
msgid "Settings"
msgstr "Configuration"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "States"
msgstr "États"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__state
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__state
msgid "Status"
msgstr "Statut"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__struct_id
msgid "Structure"
msgstr "Structure"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""
"Le code des règles salariales peuvent être utilisés comme référence dans le "
"calcul d'autres règles. Dans ce cas, il est sensible à la casse."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_rule_input__code
msgid "The code that can be used in the salary rules"
msgstr "Code qui peut être utilisé dans les règles salariales"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__amount_select
msgid "The computation method for the rule amount."
msgstr "La méthode de calcul pour la règle de montant"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_worked_days__contract_id
msgid "The contract for which applied this input"
msgstr "Le contrat auquel s'applique cette entrée"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__condition_range_max
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr "Montant maximum, appliqué pour cette règle"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__condition_range_min
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr "Le montant minimum appliqué pour cette règle"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__condition_range
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic, "
"but you can also use categories code fields in lowercase as a variable names "
"(hra, ma, lta, etc.) and the variable basic."
msgstr ""
"Sera utilisé pour calculer le % des valeurs des champs, en général "
"s'applique à la base, mais vous pouvez également utiliser les catégories de "
"champs de code en minuscules  en tant que nom de variables (hra, ma, Ita, "
"etc.) et la base variable."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__total
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Total"
msgstr "Total"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Total Working Days"
msgstr "Nb. jours travaillés"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__upper_bound
msgid "Upper Bound"
msgstr "Limite supérieure"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_contract_advantage_template__upper_bound
msgid "Upper bound authorized by the employer for this advantage"
msgstr "La limite supérieure autorisée par l'employeur pour cet avantage"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__sequence
msgid "Use to arrange calculation sequence"
msgstr "À utiliser pour définir les séquences de calcul"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr "Utilisé pour montrer la règle de salaire sur la fiche de paie."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip__state__verify
msgid "Waiting"
msgstr "En attente"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__weekly
msgid "Weekly"
msgstr "Hebdomadaire"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Worked Day"
msgstr "Jour travaillé"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Worked Days"
msgstr "Jours travaillés"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Worked Days & Inputs"
msgstr "Jours travaillés et entrées"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract__resource_calendar_id
msgid "Working Schedule"
msgstr "Heures de travail"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong percentage base or quantity defined for salary rule %s (%s)."
msgstr ""
"Mauvaise base de pourcentage ou quantité définie pour la règle de salaire %s "
"(%s)"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong python code defined for salary rule %s (%s)."
msgstr "Mauvais code python défini pour la règle de salaire %s (%s)."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong python condition defined for salary rule %s (%s)."
msgstr "Mauvaise condition python définie pour la règle de salaire %s (%s)."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong quantity defined for salary rule %s (%s)."
msgstr "Mauvaise quantité définie pour la règle de salaire %s (%s)"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong range condition defined for salary rule %s (%s)."
msgstr ""
"Mauvaise condition de l'intervalle défini pour la règle de salaire %s (%s)."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payroll_structure.py:0
#, python-format
msgid "You cannot create a recursive salary structure."
msgstr "Vous ne pouvez pas créer une structure de salaire récursive."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "You cannot delete a payslip which is not draft or cancelled"
msgstr ""

#. module: l10n_tunisia_payroll
#: code:addons/payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "You must select employee(s) to generate payslip(s)."
msgstr ""
"Vous devez sélectionnner un (des) employé(s) pour générer une (des) fiche(s) "
"de paie."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip_line.py:0
#, python-format
msgid "You must set a contract to create a payslip line."
msgstr ""
"Vous devez définir un contrat pour créer une ligne de bulletin de paie."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__amount_percentage_base
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__amount_percentage_base
msgid "result will be affected to a variable"
msgstr "le résultat sera affecté à une variable"

#~ msgid ""
#~ "* When the payslip is created the status is 'Draft'\n"
#~ "                \n"
#~ "* If the payslip is under verification, the status is 'Waiting'.\n"
#~ "                \n"
#~ "* If the payslip is confirmed then status is set to 'Done'.\n"
#~ "                \n"
#~ "* When user cancel payslip the status is 'Rejected'."
#~ msgstr ""
#~ "* Quand la fiche de paie est créée, le statut est 'Brouillon'.\n"
#~ "* Si la fiche de paie est à contrôler, le statut est 'En Attente'.\n"
#~ "* Si la fiche de paie est confirmée, le statut est 'Terminée'.\n"
#~ "* Quand l'utilisateur annule la fiche de paie, le statut est 'Rejetée'."

#~ msgid "<span class=\"o_form_label\">Payroll Rules</span>"
#~ msgstr "<span class=\"o_form_label\">Règles de Paie</span>"

#~ msgid "Account"
#~ msgstr "Compte"

#~ msgid "Belgium Payroll"
#~ msgstr "Règles de paie belge"

#~ msgid "Choose a Payroll Localization"
#~ msgstr "Choisissez une localisation pour les règles de paie"

#~ msgid "Conveyance Allowance For Gravie"
#~ msgstr "Indemnité de transport pour Gravie"

#~ msgid "French Payroll"
#~ msgstr "Paie française"

#~ msgid "Indian Payroll"
#~ msgstr "Paie Indienne"

#~ msgid "Payroll rules that apply to your country"
#~ msgstr "Règles de paie qui s'appliquent dans votre pays"

#~ msgid "Payslip 'Date From' must be earlier 'Date To'."
#~ msgstr "La date de début de la fiche de paie doit précéder la date de fin."

#~ msgid "Refund: "
#~ msgstr "Remboursement : "

#~ msgid "You cannot delete a payslip which is not draft or cancelled!"
#~ msgstr ""
#~ "Vous ne pouvez supprimer une fiche de paie qui ne soit ni brouillon ni "
#~ "annulée!"
