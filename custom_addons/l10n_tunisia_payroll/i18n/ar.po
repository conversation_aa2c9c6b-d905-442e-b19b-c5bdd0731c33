# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_payroll
#
# Translators:
# <AUTHOR> <EMAIL>, 2018
# <PERSON> <m.i<PERSON><EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <o<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON>kra<PERSON> <<EMAIL>>, 2018
# <PERSON>, 2018
# hoxhe <PERSON>ts <<EMAIL>>, 2018
# <AUTHOR> <EMAIL>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2018
# <PERSON> <<EMAIL>>, 2019
# <AUTHOR> <EMAIL>, 2019
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-10-08 23:24+0000\n"
"PO-Revision-Date: 2018-08-24 09:19+0000\n"
"Last-Translator: Osoul <<EMAIL>>, 2019\n"
"Language-Team: Arabic (https://www.transifex.com/odoo/teams/41243/ar/)\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payroll_structure.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (نسخة)"

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,print_report_name:l10n_tunisia_payroll.action_report_payslip
msgid "'Payslip - %s' % (object.employee_id.name)"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,print_report_name:l10n_tunisia_payroll.payslip_details_report
msgid "'Payslip Details - %s' % (object.employee_id.name)"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip__state
msgid ""
"* When the payslip is created the status is 'Draft'\n"
"        \n"
"* If the payslip is under verification, the status is 'Waiting'.\n"
"        \n"
"* If the payslip is confirmed then status is set to 'Done'.\n"
"        \n"
"* When user cancel payslip the status is 'Rejected'."
msgstr ""

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_hr_payslip_by_employees
msgid ""
"<span colspan=\"4\" nolabel=\"1\">This wizard will generate payslips for all "
"selected employee(s) based on the dates and credit note specified on "
"Payslips Run.</span>"
msgstr ""
"<span colspan=\"4\" nolabel=\"1\">سينشئ هذا المعالج إيصالات بمرتبات كافة "
"الموظفين المختارين حسب التواريخ وإشعارات الخصم المحددة بفترة الإيصال.</span>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Address</strong>"
msgstr "<strong>العنوان</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Authorized signature</strong>"
msgstr "<strong>توقيع معتمد</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Bank Account</strong>"
msgstr "</strong>حساب بنكي<strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "<strong>Date From:</strong>"
msgstr "<strong>التاريخ من:</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Date From</strong>"
msgstr "<strong>التاريخ من</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "<strong>Date To:</strong>"
msgstr "<strong>التاريخ إلى:</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Date To</strong>"
msgstr "<strong>التاريخ إلى</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Designation</strong>"
msgstr "<strong>التعيين</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Email</strong>"
msgstr "<strong>البريد الإلكتروني</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Identification No</strong>"
msgstr "<strong>رقم الهوية</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Name</strong>"
msgstr "<strong>الاسم</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "<strong>Reference</strong>"
msgstr "<strong>رقم الإشارة</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "<strong>Register Name:</strong>"
msgstr "<strong>اسم السجل:</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "<strong>Total</strong>"
msgstr "<strong>الإجمالي</strong>"

#. module: l10n_tunisia_payroll
#: model_terms:ir.actions.act_window,help:l10n_tunisia_payroll.hr_contribution_register_action
msgid ""
"A contribution register is a third party involved in the salary\n"
"            payment of the employees. It can be the social security, the\n"
"            state or anyone that collect or inject money on payslips."
msgstr ""
"سجل المساهمة هو طرف ثالث يشارك في دفع\n"
"            رواتب الموظفين. يمكن أن يكون الضمان الاجتماعي أو\n"
"            الدولة أو أي شخص يقوم بجمع أو ضخ الأموال في إيصالات المرتبات."

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.res_config_settings_view_form
msgid "Accounting"
msgstr "المحاسبة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Accounting Information"
msgstr "معلومات محاسبية"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__active
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__active
msgid "Active"
msgstr "نشط"

#. module: l10n_tunisia_payroll
#: model_terms:ir.actions.act_window,help:l10n_tunisia_payroll.hr_contribution_register_action
msgid "Add a new contribution register"
msgstr "إضافة سجل مساهمة جديد"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Add an internal note..."
msgstr "إضافة ملاحظة داخلية..."

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contract_advantage_template_view_form
msgid "Advantage Name"
msgstr "اسم الميزة"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.act_children_salary_rules
msgid "All Children Rules"
msgstr "كافة القواعد التابعة"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.ALW
msgid "Allowance"
msgstr "بدل"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__condition_select__none
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__condition_select__none
msgid "Always True"
msgstr "صحيح دائمًا"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__amount
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Amount"
msgstr "المبلغ"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_select
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
msgid "Amount Type"
msgstr "نوع المبلغ"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__annually
msgid "Annually"
msgstr "سنويًا"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Appears on Payslip"
msgstr "يظهر في إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__condition_python
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__condition_python
msgid ""
"Applied this rule for calculation if condition is true. You can specify "
"condition like basic > 1000."
msgstr ""
"تُطبق هذه القاعدة للحساب في حال تم تطبيق الشرط. يمكنك تحديد شرط مثل أن يكون "
"المرتب الأساسي >1000."

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.BASIC
msgid "Basic"
msgstr "أساسي"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_rule_basic
msgid "Basic Salary"
msgstr "المرتب الأساسي"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__bi-monthly
msgid "Bi-monthly"
msgstr "كل شهرين"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__bi-weekly
msgid "Bi-weekly"
msgstr "كل أسبوعين"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_form
msgid "Calculations"
msgstr "حسابات"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_payslip_lines_contribution_register
msgid "Cancel"
msgstr "إلغاء"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Cancel Payslip"
msgstr "إلغاء إيصال المرتب"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Cannot cancel a payslip that is done."
msgstr "لا يمكن إلغاء إيصال مرتب مكتمل."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__category_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__category_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_search
msgid "Category"
msgstr "الفئة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Child Rules"
msgstr "القواعد التابعة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__child_ids
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__child_ids
msgid "Child Salary Rule"
msgstr "قاعدة المرتب التابعة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__children_ids
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__children_ids
msgid "Children"
msgstr "الفروع"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Children Definition"
msgstr "التعريفات التابعة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_run__state__close
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
msgid "Close"
msgstr "إقفال"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__code
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Code"
msgstr "الكود"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_kanban
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_kanban
msgid "Code:"
msgstr "الكود:"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Companies"
msgstr "المؤسسات"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__company_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__company_id
msgid "Company"
msgstr "المؤسسة"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.COMP
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Company Contribution"
msgstr "مساهمة المؤسسة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Computation"
msgstr "احتساب"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Compute Sheet"
msgstr "ورقة الحساب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_select
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_select
msgid "Condition Based on"
msgstr "بناء على شرط"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Conditions"
msgstr "الشروط"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_res_config_settings
msgid "Config Settings"
msgstr "ضبط الإعدادات"

#. module: l10n_tunisia_payroll
#: model:ir.ui.menu,name:l10n_tunisia_payroll.payroll_menu_configuration
msgid "Configuration"
msgstr "الإعدادات"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Confirm"
msgstr "تأكيد"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__contract_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__contract_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__contract_id
msgid "Contract"
msgstr "العقد"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_contract_advantage_template_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_contract_advantage_template_menu_action
msgid "Contract Advantage Templates"
msgstr "قوالب مزايا العقود"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contribution_register_view_form
msgid "Contribution"
msgstr "المساهمة"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_contribution_register
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__register_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__register_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
msgid "Contribution Register"
msgstr "سجل المساهمة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_payslip_lines_contribution_register
msgid "Contribution Register's Payslip Lines"
msgstr "بنود إيصال سجل المساهمة"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_contribution_register_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_contribution_register_menu
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contribution_register_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contribution_register_view_tree
msgid "Contribution Registers"
msgstr "سجلات المساهمة"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_convanceallowance1
msgid "Conveyance Allowance"
msgstr "بدل النقل"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_ca_demo
msgid "Conveyance Allowance For Marc Demo"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__create_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__create_uid
msgid "Created by"
msgstr "أنشئ بواسطة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__create_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__create_date
msgid "Created on"
msgstr "أنشئ في"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__credit_note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__credit_note
msgid "Credit Note"
msgstr "إشعار خصم"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__date_from
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__date_start
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__date_from
msgid "Date From"
msgstr "التاريخ من"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__date_to
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__date_end
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__date_to
msgid "Date To"
msgstr "التاريخ إلى"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.DED
msgid "Deduction"
msgstr "الاستقطاعات"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__default_value
msgid "Default value for this advantage"
msgstr "القيمة الافتراضية لهذه الميزة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_contract__schedule_pay
msgid "Defines the frequency of the wage payment."
msgstr "يحدد وتيرة دفع الأجور."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip__struct_id
msgid ""
"Defines the rules that have to be applied to this payslip, accordingly to "
"the contract chosen. If you let empty the field contract, this field isn't "
"mandatory anymore and thus the rules applied will be all the rules set on "
"the structure of all contracts of the employee valid for the chosen period"
msgstr ""
"يحدد القواعد التي يجب تطبيقها على إيصال المرتب هذا، حسب العقد المختار. إذا "
"تركت حقل العقد فارغًا، لن يكون هذا الحقل إلزاميًا، وبالتالي ستُطبق كافة القواعد "
"المنصوص عليها في هيكل المرتب فى عقود الموظف الصالحة للفترة المختارة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__note
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__note
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_contribution_register_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Description"
msgstr "الوصف"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Details By Salary Rule Category"
msgstr "التفاصيل حسب فئة قواعد المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__details_by_salary_rule_category
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Details by Salary Rule Category"
msgstr "التفاصيل حسب فئة قواعد المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_contributionregister__display_name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_payslipdetails__display_name
msgid "Display Name"
msgstr "الاسم المعروض"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip__state__done
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Done"
msgstr "المنتهية"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
msgid "Done Payslip Batches"
msgstr "دفعات المرتب المكتملة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Done Slip"
msgstr "إيصال المرتب المنتهي"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip__state__draft
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_run__state__draft
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Draft"
msgstr "مسودة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
msgid "Draft Payslip Batches"
msgstr "دفعات المرتب المسودة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Draft Slip"
msgstr "إيصال مرتب مسودة"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_employee
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__employee_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__employee_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Employee"
msgstr "الموظف"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_contract
msgid "Employee Contract"
msgstr "عقد الموظف"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_tree
msgid "Employee Function"
msgstr "مهمات الموظف"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_payslip_menu
msgid "Employee Payslips"
msgstr "إيصالات مرتبات الموظف"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_contract_advantage_template
msgid "Employee's Advantage on Contract"
msgstr "مزايا الموظف المسجلة بالعقد"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_contract__resource_calendar_id
msgid "Employee's working schedule."
msgstr "جدول عمل الموظف."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__employee_ids
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_hr_payslip_by_employees
msgid "Employees"
msgstr "الموظفون"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule_category.py:0
#, python-format
msgid "Error! You cannot create recursive hierarchy of Salary Rule Category."
msgstr "خطأ! لا يمكنك إنشاء تدرج هرمي متكرر لفئة قواعد المرتب."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Error! You cannot create recursive hierarchy of Salary Rules."
msgstr "خطأ! لا يمكنك إنشاء تدرج هرمي متكرر لقواعد المرتب."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__register_id
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__register_id
msgid "Eventual third party involved in the salary payment of the employees."
msgstr "طرف ثالث نهائي يشارك في دفع أجور الموظفين ."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_fix
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_fix
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__amount_select__fix
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__amount_select__fix
msgid "Fixed Amount"
msgstr "مبلغ ثابت"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__amount_percentage
msgid "For example, enter 50.0 to apply a percentage of 50%"
msgstr "على سبيل المثال، ادخل 50.0 لتطبيق نسبة 50%"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/report/report_contribution_register.py:0
#, python-format
msgid "Form content is missing, this report cannot be printed."
msgstr "بعض بيانات النموذج فارغة، لا يمكن طباعة هذا التقرير."

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "General"
msgstr "عام"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_hr_payslip_by_employees
msgid "Generate"
msgstr "إنشاء"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.action_hr_payslip_by_employees
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
msgid "Generate Payslips"
msgstr "إنشاء إيصالات مرتبات"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_employees
msgid "Generate payslips for all selected employees"
msgstr "إنشاء إيصالات مرتبات لكافة الموظفين المحددين"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_sales_commission
msgid "Get 1% of sales"
msgstr "احصل على 1% من المبيعات"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Global Leaves"
msgstr "الإجازات العامة"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_rule_taxable
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.GROSS
msgid "Gross"
msgstr "الإجمالي"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_search
msgid "Group By"
msgstr "تجميع حسب"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_houserentallowance1
msgid "House Rent Allowance"
msgstr "بدل سكن"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_contributionregister__id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_payslipdetails__id
msgid "ID"
msgstr "المعرف"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_run__credit_note
msgid ""
"If its checked, indicates that all payslips generated from here are refund "
"payslips."
msgstr ""
"إذا كان محددًا، فهذا يشير إلى أن جميع إيصالات المرتبات المنشأة من هنا هي "
"إيصالات مرتجعات."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__active
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__active
msgid ""
"If the active field is set to false, it will allow you to hide the salary "
"rule without removing it."
msgstr ""
"إذا تم تحويل قيمة الحقل نشط إلى خطأ، يمكنك إخفاء قاعدة المرتب دون إزالتها."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip__credit_note
msgid "Indicates this payslip has a refund of another"
msgstr "تشير إلى أن لهذا الإيصال قيمة مسترجعة من إيصال آخر"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Input Data"
msgstr "البيانات المدخلة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__input_ids
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__input_ids
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
msgid "Inputs"
msgstr "المدخلات"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__note
msgid "Internal Note"
msgstr "ملاحظة داخلية"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_kanban
msgid "Is a Blocking Reason?"
msgstr "هل هو سبب للحظر؟"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__quantity
msgid ""
"It is used in computation for percentage and fixed amount. For e.g. A rule "
"for Meal Voucher having fixed amount of 1€ per worked day can have its "
"quantity defined in expression like worked_days.WORK100.number_of_days."
msgstr ""
"تستخدم في احتساب الكميات الثابتة وذات النسبة المئوية. فمثلًا: يمكن تعريف "
"قاعدة قسيمة وجبة الطعام ذات المبلغ الثابت بواحد يورو لكل يوم عمل بصيغة مثل: "
"worked_days.WORK100.number_of_days."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_input__amount
msgid ""
"It is used in computation. For e.g. A rule for sales having 1% commission of "
"basic salary for per product can defined in expression like result = inputs."
"SALEURO.amount * contract.wage*0.01."
msgstr ""
"تستخدم في الاحتساب. مثلًا: يمكن تعريف قاعدة مبيعات تنص على عمولة بقدر 1٪ من "
"المرتب الأساسي لكل منتج بالتعبير: result = inputs.SALEURO.amount * contract."
"wage*0.01."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_contributionregister____last_update
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_report_payroll_report_payslipdetails____last_update
msgid "Last Modified on"
msgstr "آخر تعديل في"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__write_uid
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__write_uid
msgid "Last Updated by"
msgstr "آخر تحديث بواسطة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_employees__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__write_date
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_payslip_lines_contribution_register__write_date
msgid "Last Updated on"
msgstr "آخر تحديث في"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule_category__parent_id
msgid ""
"Linking a salary category to its parent is used only for the reporting "
"purpose."
msgstr "يستخدم ربط فئة المرتب بفئته الأصل بغرض تقديم التقارير فقط."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__lower_bound
msgid "Lower Bound"
msgstr "الحد الأدنى"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_contract_advantage_template__lower_bound
msgid "Lower bound authorized by the employer for this advantage"
msgstr "الحد الأدنى المعتمد من الموظف لهذه الميزة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__paid
msgid "Made Payment Order ? "
msgstr "أصدر أمر سداد؟ "

#. module: l10n_tunisia_payroll
#: model:ir.module.category,description:l10n_tunisia_payroll.module_category_payroll
msgid "Manage employee payroll"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:res.groups,name:l10n_tunisia_payroll.group_payroll_manager
msgid "Manager"
msgstr "المدير"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_range_max
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_range_max
msgid "Maximum Range"
msgstr "الحد الأقصى"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_meal_voucher
msgid "Meal Voucher"
msgstr "قسيمة وجبة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_range_min
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_range_min
msgid "Minimum Range"
msgstr "الحد الأدنى"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Miscellaneous"
msgstr "المتنوعة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__monthly
msgid "Monthly"
msgstr "شهريًا"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__name
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__name
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Name"
msgstr "الاسم"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule.category,name:l10n_tunisia_payroll.NET
msgid "Net"
msgstr "الصافي"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_rule_net
msgid "Net Salary"
msgstr "صافي المرتب"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Normal Working Days paid at 100%"
msgstr "أيام العمل العادية المدفوعة بنسبة 100%"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_category_view_form
msgid "Notes"
msgstr "ملاحظات"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__number_of_days
msgid "Number of Days"
msgstr "عدد الأيام"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__number_of_hours
msgid "Number of Hours"
msgstr "عدد الساعات"

#. module: l10n_tunisia_payroll
#: model:res.groups,name:l10n_tunisia_payroll.group_payroll_user
msgid "Officer"
msgstr "موظف"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Other Inputs"
msgstr "مدخلات أخرى"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__parent_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule_category__parent_id
msgid "Parent"
msgstr "الأصل"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__parent_rule_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__parent_rule_id
msgid "Parent Salary Rule"
msgstr "قاعدة المرتب الأصلية"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__partner_id
msgid "Partner"
msgstr "الشريك"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__payslip_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__slip_id
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__payslip_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Pay Slip"
msgstr "إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "PaySlip Batch"
msgstr "دفعات إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,name:l10n_tunisia_payroll.payslip_details_report
msgid "PaySlip Details"
msgstr "تفاصيل إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.action_payslip_lines_contribution_register
msgid "PaySlip Lines"
msgstr "بنود إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,name:l10n_tunisia_payroll.action_contribution_register
msgid "PaySlip Lines By Conribution Register"
msgstr "بنود إيصال المرتب حسب سجل المساهمة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "PaySlip Lines by Contribution Register"
msgstr "بنود إيصال المرتب حسب سجل المساهمة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "PaySlip Name"
msgstr "اسم إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.open_payroll_modules
#: model:ir.module.category,name:l10n_tunisia_payroll.module_category_payroll
#: model:ir.ui.menu,name:l10n_tunisia_payroll.payroll_menu_root
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.res_config_settings_view_form
msgid "Payroll"
msgstr "الأجور"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_res_config_settings__module_payroll_account
msgid "Payroll Accounting"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_report_payroll_report_contributionregister
msgid "Payroll Contribution Register Report"
msgstr "تقرير سجل المساهمة في الأجور"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.res_config_settings_view_form
msgid "Payroll Entries"
msgstr "قيود الأجور"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_search
msgid "Payroll Structures"
msgstr "هياكل الأجور"

#. module: l10n_tunisia_payroll
#: model:ir.actions.report,name:l10n_tunisia_payroll.action_report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Payslip"
msgstr "إيصال المرتب"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Payslip 'Date From' must be earlier than 'Date To'."
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_run
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__payslip_run_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
msgid "Payslip Batches"
msgstr "دفعات إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_line_action_computation_details
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__payslip_count
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Payslip Computation Details"
msgstr "تفاصيل احتساب إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_employee__payslip_count
msgid "Payslip Count"
msgstr "عدد إيصالات المرتبات"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_report_payroll_report_payslipdetails
msgid "Payslip Details Report"
msgstr "تقرير تفاصيل إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_input
msgid "Payslip Input"
msgstr "مدخلات إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__input_line_ids
msgid "Payslip Inputs"
msgstr "مدخلات إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_line
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Payslip Line"
msgstr "بند إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_line_action
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__line_ids
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Payslip Lines"
msgstr "بنود إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Payslip Lines by Contribution Register"
msgstr "بنود إيصال المرتب حسب سجل المساهمة"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_payslip_lines_contribution_register
msgid "Payslip Lines by Contribution Registers"
msgstr "بنود إيصال المرتب حسب سجل المساهمة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__name
msgid "Payslip Name"
msgstr "اسم إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payslip_worked_days
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__worked_days_line_ids
msgid "Payslip Worked Days"
msgstr "أيام العمل في إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_action_employee
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_employee__slip_ids
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__slip_ids
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_employee_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_tree
msgid "Payslips"
msgstr "إيصالات المرتبات"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payslip_run_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_payslip_run_menu
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_tree
msgid "Payslips Batches"
msgstr "دفعات إيصالات المرتبات"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_hr_payslip_by_employees
msgid "Payslips by Employees"
msgstr "إيصالات المرتبات حسب الموظفين"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_percentage
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_percentage
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__amount_select__percentage
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__amount_select__percentage
msgid "Percentage (%)"
msgstr "النسبة المئوية (%)"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_percentage_base
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_percentage_base
msgid "Percentage based on"
msgstr "النسبة المئوية بناء على"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Period"
msgstr "الفترة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.res_config_settings_view_form
msgid "Post payroll slips in accounting"
msgstr "ترحيل إيصالات المرتبات في المحاسبة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.view_payslip_lines_contribution_register
msgid "Print"
msgstr "طباعة"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_professionaltax1
msgid "Professional Tax"
msgstr "الضريبة المهنية"

#. module: l10n_tunisia_payroll
#: model:hr.salary.rule,name:l10n_tunisia_payroll.hr_salary_rule_providentfund1
msgid "Provident Fund"
msgstr "صندوق الإدخار"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__amount_python_compute
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__amount_python_compute
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__amount_select__code
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__amount_select__code
msgid "Python Code"
msgstr "كود بايثون"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_python
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_python
msgid "Python Condition"
msgstr "شرط بايثون"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__condition_select__python
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__condition_select__python
msgid "Python Expression"
msgstr "تعبير لغة بايثون"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__quantity
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__quantity
msgid "Quantity"
msgstr "الكمية"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
msgid "Quantity/Rate"
msgstr "الكمية/النسبة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Quantity/rate"
msgstr "الكمية/النسبة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__quarterly
msgid "Quarterly"
msgstr "ربع سنوي"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip_line__condition_select__range
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_salary_rule__condition_select__range
msgid "Range"
msgstr "المدى"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__condition_range
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__condition_range
msgid "Range Based on"
msgstr "المدى مبني على"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__rate
msgid "Rate (%)"
msgstr "المعدل (%)"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__code
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__number
msgid "Reference"
msgstr "رقم الإشارة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Refund"
msgstr "المرتجع"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Refund: %s"
msgstr ""

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contribution_register__register_line_ids
msgid "Register Line"
msgstr "بند السجل"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip__state__cancel
msgid "Rejected"
msgstr "مرفوض"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__salary_rule_id
msgid "Rule"
msgstr "قاعدة"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_category_view_form
msgid "Salary Categories"
msgstr "فئات المرتب"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Salary Computation"
msgstr "احتساب المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_salary_rule
msgid "Salary Rule"
msgstr "قاعدة المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_salary_rule_category_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.menu_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_category_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_category_view_tree
msgid "Salary Rule Categories"
msgstr "فئات قواعد المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_salary_rule_category
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Salary Rule Category"
msgstr "فئة قواعد المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_rule_input
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_rule_input__input_id
msgid "Salary Rule Input"
msgstr "مدخلات قاعدة المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.action_salary_rule_form
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payroll_structure__rule_ids
#: model:ir.ui.menu,name:l10n_tunisia_payroll.menu_action_hr_salary_rule_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_search
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_tree_children
msgid "Salary Rules"
msgstr "قواعد المرتبات"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "Salary Slip of %s for %s"
msgstr "قسيمة مرتب %s لـ%s"

#. module: l10n_tunisia_payroll
#: model:ir.model,name:l10n_tunisia_payroll.model_hr_payroll_structure
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract__struct_id
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payroll_structure_view_tree_children
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_tree
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Salary Structure"
msgstr "هيكل المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.hr_payroll_structure_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.hr_payroll_structure_menu
msgid "Salary Structures"
msgstr "هياكل المرتبات"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract__schedule_pay
msgid "Scheduled Pay"
msgstr "الدفع المجدول"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_search
msgid "Search Payslip Batches"
msgstr "البحث في دفعات إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_line_view_search
msgid "Search Payslip Lines"
msgstr "البحث في بنود إيصال المرتب"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "Search Payslips"
msgstr "البحث في إيصالات المرتبات"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_salary_rule_view_search
msgid "Search Salary Rule"
msgstr "البحث في قواعد المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__semi-annually
msgid "Semi-annually"
msgstr "نصف سنوي"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_input__sequence
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_worked_days__sequence
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_salary_rule__sequence
msgid "Sequence"
msgstr "التسلسل"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_run_view_form
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Set to Draft"
msgstr "تعيين كمسودة"

#. module: l10n_tunisia_payroll
#: model:ir.actions.act_window,name:l10n_tunisia_payroll.payroll_configuration_action
#: model:ir.ui.menu,name:l10n_tunisia_payroll.menu_payroll_global_settings
msgid "Settings"
msgstr "الإعدادات"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_search
msgid "States"
msgstr "الحالات"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__state
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_run__state
msgid "Status"
msgstr "الحالة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip__struct_id
msgid "Structure"
msgstr "الهيكل"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__code
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__code
msgid ""
"The code of salary rules can be used as reference in computation of other "
"rules. In that case, it is case sensitive."
msgstr ""
"يمكن استخدام كود قواعد المرتب كمرجع في حساب قواعد أخرى. في هذه الحالة، يكون "
"الكود مميزًا حسب حالة الحرف."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_input__code
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_worked_days__code
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_rule_input__code
msgid "The code that can be used in the salary rules"
msgstr "الرمز الذي يمكن استخدامه في قواعد المرتب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__amount_select
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__amount_select
msgid "The computation method for the rule amount."
msgstr "طريقة حساب لكمية القاعدة."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_input__contract_id
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_worked_days__contract_id
msgid "The contract for which applied this input"
msgstr "العقد الذى تطبق عليه هذه المدخلات"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__condition_range_max
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__condition_range_max
msgid "The maximum amount, applied for this rule."
msgstr "الحد الأقصى للكمية الذي تُطبق عليها هذه القاعدة."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__condition_range_min
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__condition_range_min
msgid "The minimum amount, applied for this rule."
msgstr "الحد الأدنى المطلوب للكمية لتطبيق لهذه القاعدة."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__condition_range
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__condition_range
msgid ""
"This will be used to compute the % fields values; in general it is on basic, "
"but you can also use categories code fields in lowercase as a variable names "
"(hra, ma, lta, etc.) and the variable basic."
msgstr ""
"سيستخدم هذا في احتساب قيم حقول %s؛ وبشكل عام فهي تنطبق على أساس الأجر لكن "
"بإمكانك أن تستخدم كود الفئة مكتوباً بحروف صغيرة كأسماء للمتغيرات (مثل: hra, "
"ma, lta وغيرها) بالإضافة لمتغير الأجر الأساسي."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_payslip_line__total
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_contributionregister
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslip
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.report_payslipdetails
msgid "Total"
msgstr "الإجمالي"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Total Working Days"
msgstr "إجمالي أيام العمل"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract_advantage_template__upper_bound
msgid "Upper Bound"
msgstr "الحد الأقصى"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_contract_advantage_template__upper_bound
msgid "Upper bound authorized by the employer for this advantage"
msgstr "الحد الأقصى المعتمد من الموظف لهذه الميزة"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__sequence
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__sequence
msgid "Use to arrange calculation sequence"
msgstr "تستخدم لترتيب متتابعة الحساب"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__appears_on_payslip
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__appears_on_payslip
msgid "Used to display the salary rule on payslip."
msgstr "تستخدم لعرض قاعدة المرتب على إيصال المرتب."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_payslip__state__verify
msgid "Waiting"
msgstr "في الانتظار"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields.selection,name:l10n_tunisia_payroll.selection__hr_contract__schedule_pay__weekly
msgid "Weekly"
msgstr "أسبوعيًا"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Worked Day"
msgstr "يوم العمل"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Worked Days"
msgstr "أيام العمل"

#. module: l10n_tunisia_payroll
#: model_terms:ir.ui.view,arch_db:l10n_tunisia_payroll.hr_payslip_view_form
msgid "Worked Days & Inputs"
msgstr "أيام العمل والمدخلات"

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,field_description:l10n_tunisia_payroll.field_hr_contract__resource_calendar_id
msgid "Working Schedule"
msgstr "جدول العمل"

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong percentage base or quantity defined for salary rule %s (%s)."
msgstr "كمية أو قيمة النسبة المئوية المحددة لقاعدة المرتب %s (%s) خطأ."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong python code defined for salary rule %s (%s)."
msgstr "كود بايثون المحدد لقاعدة المرتب %s (%s) خطأ."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong python condition defined for salary rule %s (%s)."
msgstr "شرط بايثون المحدد لقاعدة المرتب %s (%s) خطأ."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong quantity defined for salary rule %s (%s)."
msgstr "الكمية المحددة لقاعدة المرتب %s (%s) خطأ."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_salary_rule.py:0
#, python-format
msgid "Wrong range condition defined for salary rule %s (%s)."
msgstr "مدى تطبيق الشرط المحدد لقاعدة المرتب %s (%s) خطأ."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payroll_structure.py:0
#, python-format
msgid "You cannot create a recursive salary structure."
msgstr "لا يمكنك إنشاء بنية متكررة للمرتب."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip.py:0
#, python-format
msgid "You cannot delete a payslip which is not draft or cancelled"
msgstr ""

#. module: l10n_tunisia_payroll
#: code:addons/payroll/wizard/hr_payroll_payslips_by_employees.py:0
#, python-format
msgid "You must select employee(s) to generate payslip(s)."
msgstr "يجب تحديد موظف لإنشاء إيصالات المرتبات."

#. module: l10n_tunisia_payroll
#: code:addons/payroll/models/hr_payslip_line.py:0
#, python-format
msgid "You must set a contract to create a payslip line."
msgstr "يجب تحديد عقد لإنشاء بند إيصال المرتب."

#. module: l10n_tunisia_payroll
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_payslip_line__amount_percentage_base
#: model:ir.model.fields,help:l10n_tunisia_payroll.field_hr_salary_rule__amount_percentage_base
msgid "result will be affected to a variable"
msgstr "النتيجة سوف تتأثر بمتغير"

#~ msgid ""
#~ "* When the payslip is created the status is 'Draft'\n"
#~ "                \n"
#~ "* If the payslip is under verification, the status is 'Waiting'.\n"
#~ "                \n"
#~ "* If the payslip is confirmed then status is set to 'Done'.\n"
#~ "                \n"
#~ "* When user cancel payslip the status is 'Rejected'."
#~ msgstr ""
#~ "* عند إنشاء إيصال مرتب يكون في الحالة 'مسودة'\n"
#~ "                \n"
#~ "* إذا كان الإيصال قيد التحقق ينتقل للحالة 'انتظار'.\n"
#~ "                \n"
#~ "* اذا تم تأكيد الإيصال ينتقل للحالة 'مكتمل'.\n"
#~ "                \n"
#~ "* عنمما يقوم المستخدم بإلغاء إيصال المرتب تكون حالته 'مرفوض'."

#~ msgid "<span class=\"o_form_label\">Payroll Rules</span>"
#~ msgstr "<span class=\"o_form_label\">قواعد الأجور</span>"

#~ msgid "Account"
#~ msgstr "الحساب"

#~ msgid "Belgium Payroll"
#~ msgstr "أجور بلجيكا"

#~ msgid "Choose a Payroll Localization"
#~ msgstr "اختيار توطين الأجور"

#~ msgid "Conveyance Allowance For Gravie"
#~ msgstr "بدل النقل لجرافي"

#~ msgid "French Payroll"
#~ msgstr "الرواتب الفرنسية"

#~ msgid "Indian Payroll"
#~ msgstr "الرواتب الهندية"

#~ msgid "Payroll rules that apply to your country"
#~ msgstr "قواعد الأجور المطبقة في بلدك"

#~ msgid "Payslip 'Date From' must be earlier 'Date To'."
#~ msgstr "يجب أن يسبق 'تاريخ البداية' في إيصال المرتب 'تاريخ النهاية'."

#~ msgid "Refund: "
#~ msgstr "المرتجع: "

#~ msgid "You cannot delete a payslip which is not draft or cancelled!"
#~ msgstr "لا يمكنك حذف إيصال مرتب ما لم يكن في حالة المسودة أو ملغيًا!"
